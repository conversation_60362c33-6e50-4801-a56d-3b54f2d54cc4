import 'package:animated_bottom_navigation_bar/animated_bottom_navigation_bar.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:smsautoforwardapp/controller/auth_controller.dart';
import 'package:smsautoforwardapp/homepage.dart';
import 'package:smsautoforwardapp/profile.dart';
import 'package:smsautoforwardapp/style.dart';
import 'package:url_launcher/url_launcher.dart';

import 'bottomsheet_rule.dart';
import 'logs.dart';

class BNB extends StatefulWidget {
  const BNB({
    super.key,
  });

  @override
  State<BNB> createState() => _BNBState();
}

class _BNBState extends State<BNB> with TickerProviderStateMixin {
  final AuthController authController = Get.find();
  final autoSizeGroup = AutoSizeGroup();
  var _bottomNavIndex = 0; //default index of a first screen

  final Uri _url =
      Uri.parse('https://smsautoforwarder.com/frequently-asked-questions/');

  final iconList = <IconData>[
    Icons.home,
    Icons.history,
    Icons.help_outline,
    Icons.person,
  ];

  Future<void> _launchUrl() async {
    if (!await canLaunchUrl(Uri.parse(_url.toString()))) {
      throw Exception('Could not launch $_url');
    }
    await launchUrl(Uri.parse(_url.toString()));
  }

  static final List<Widget> _widgetOptions = <Widget>[
    const Homepage(),
    const Logs(),
    Container(),
    const Profile()
  ];
  void checkForUpdate() async {
    final info = await PackageInfo.fromPlatform();
    final currentVersion = info.version;

    final doc = await FirebaseFirestore.instance
        .collection('config')
        .doc('version')
        .get();
    final latestVersion = doc['latestVersion'];
    final forceUpdate = doc['forceUpdate'];
    final updateUrl = doc['updateUrl'];
    final updateDialogText = doc['updateDialogText'];

    if (currentVersion != latestVersion && forceUpdate == true) {
      Get.dialog(
          barrierDismissible: false,
          PopScope(
            canPop: false,
            child: AlertDialog(
              title: const Text('Update Required'),
              content: Text(updateDialogText),
              actions: [
                ElevatedButton(
                  child: const Text(
                    'Update Now',
                    style: TextStyle(color: Colors.white),
                  ),
                  onPressed: () {
                    launchUrl(Uri.parse(updateUrl));
                  },
                ),
              ],
            ),
          ));
    }
  }

  @override
  void initState() {
    super.initState();
    checkForUpdate();

    // Fetch FCM token and update Firestore
    _fetchAndUpdateFCMToken();
  }

  Future<void> _fetchAndUpdateFCMToken() async {
    try {
      // Get FCM token
      String? token = await FirebaseMessaging.instance.getToken();
      if (token != null) {
        // Store token in Firestore
        await _saveFCMTokenToFirestore(token);
      } else {}
    } catch (e) {
      //
    }
  }

  Future<void> _saveFCMTokenToFirestore(String token) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        return;
      }

      // Only update the fcmToken field, leave other fields intact
      await FirebaseFirestore.instance.collection('users').doc(user.uid).set(
          {
            'fcmToken': token, // Only update the fcmToken field
          },
          SetOptions(
              merge: true)); // Merge ensures existing data isn't overwritten
    } catch (e) {
      //
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: bgColor,
      body: Center(
        child: _widgetOptions.elementAt(_bottomNavIndex),
      ),
      floatingActionButton: ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(50)),
        child: FloatingActionButton(
          backgroundColor: maincolor,
          onPressed: () {
            bottomSheetRule(context, null);
          },
          child: const Icon(
            Icons.add,
            color: Colors.white,
          ),
          //params
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: AnimatedBottomNavigationBar(
          icons: iconList,
          activeIndex: _bottomNavIndex,
          gapLocation: GapLocation.center,
          notchSmoothness: NotchSmoothness.smoothEdge,
          onTap: (index) {
            if (index == 2) {
              _launchUrl();
            } else {
              setState(() => _bottomNavIndex = index);
            }
          },
          activeColor: maincolor,
          inactiveColor: lightGrayColor,
          elevation: 5),
    );
  }
}
